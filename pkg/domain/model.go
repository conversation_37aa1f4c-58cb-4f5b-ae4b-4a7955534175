package domain

import "git.garena.com/shopee/anti-fraud/unified-model-service/v2/pkg/domain"

type ModelEntity struct {
	ID                int32  `gorm:"column:id" json:"id"`
	Name              string `gorm:"column:model_name" json:"model_name"`
	Version           string `gorm:"column:model_version" json:"model_version"`
	Region            string `gorm:"column:model_region" json:"model_region"`
	ServiceID         int32  `gorm:"column:service_id" json:"service_id"`
	Path              string `gorm:"column:model_path" json:"model_path"`
	LoaderType        string `gorm:"column:loader_type" json:"loader_type"`
	Status            int32  `gorm:"column:model_status" json:"model_status"`
	PostprocessConfig string `gorm:"column:postprocess_config" json:"postprocess_config"`
	ModelConfig       string `gorm:"column:model_config" json:"model_config"`
	CreateTime        uint64 `gorm:"column:create_time" json:"create_time"`
	UpdateTime        uint64 `gorm:"column:update_time" json:"update_time"`
	IsValid           int    `gorm:"column:is_valid" json:"is_valid"`
	Creator           string `gorm:"column:creator" json:"creator"`
	Updater           string `gorm:"column:updater" json:"updater"`
}

type ListModelEntity struct {
	ID                int32  `gorm:"column:id" json:"id"`
	Name              string `gorm:"column:model_name" json:"model_name"`
	Version           string `gorm:"column:model_version" json:"model_version"`
	Region            string `gorm:"column:model_region" json:"model_region"`
	ServiceID         int32  `gorm:"column:service_id" json:"service_id"`
	ServiceName       string `gorm:"column:service_name" json:"service_name"`
	Path              string `gorm:"column:model_path" json:"model_path"`
	LoaderType        string `gorm:"column:loader_type" json:"loader_type"`
	Status            int32  `gorm:"column:model_status" json:"model_status"`
	PostprocessConfig string `gorm:"column:postprocess_config" json:"postprocess_config"`
	ModelConfig       string `gorm:"column:model_config" json:"model_config"`
	CreateTime        uint64 `gorm:"column:create_time" json:"create_time"`
	UpdateTime        uint64 `gorm:"column:update_time" json:"update_time"`
	IsValid           int    `gorm:"column:is_valid" json:"is_valid"`
	Creator           string `gorm:"column:creator" json:"creator"`
	Updater           string `gorm:"column:updater" json:"updater"`
}

func (e *ListModelEntity) ToModelEntity() *domain.ModelEntity {
	return &domain.ModelEntity{
		ID:                e.ID,
		Name:              e.Name,
		Version:           e.Version,
		Region:            e.Region,
		ServiceID:         e.ServiceID,
		Path:              e.Path,
		LoaderType:        e.LoaderType,
		PostprocessConfig: e.PostprocessConfig,
		ModelConfig:       e.ModelConfig,
	}
}

type ModelStatistics struct {
	Source string `gorm:"column:feature_source" json:"source"`
	Count  int    `gorm:"column:feature_count" json:"count"`
}

type ModelsStatistics struct {
	TargetGroup string `gorm:"target_group" json:"target_group"`
	ModelCount  int    `gorm:"model_count" json:"model_count"`
}
