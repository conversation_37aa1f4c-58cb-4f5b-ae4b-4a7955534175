package util

import (
	"path/filepath"
	"strings"

	"git.garena.com/shopee/anti-fraud/ums-admin/pkg/common"
	umsUtil "git.garena.com/shopee/anti-fraud/unified-model-service/v2/pkg/util"
)

func IsDeployedInTriton(loaderType string) bool {
	switch loaderType {
	case common.LoaderTypePyTorch:
		return true

	default:
		return false
	}
}

func LocalModelConfigPath(serviceName, config string) string {
	dir := filepath.Dir(config)
	modelConfigPath := filepath.Join(dir, "config.pbtxt")
	return umsUtil.ModelRepository + serviceName + "/" + strings.TrimLeft(modelConfigPath, "/")
}
