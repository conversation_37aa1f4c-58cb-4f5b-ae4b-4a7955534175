package types

// ModelType represents the type of model for Triton Inference Server
type ModelType string

const (
	ModelTypeTensorRT   ModelType = "tensorrt_plan"
	ModelTypeTensorFlow ModelType = "tensorflow_savedmodel"
	ModelTypeONNX       ModelType = "onnxruntime_onnx"
	ModelTypePyTorch    ModelType = "pytorch_libtorch"
	ModelTypeFIL        ModelType = "fil"
	ModelTypePython     ModelType = "python"
)

// BackendType represents the backend to use for Triton Inference Server
type BackendType string

const (
	BackendTensorRT    BackendType = "tensorrt"
	BackendTensorFlow  BackendType = "tensorflow"
	BackendONNXRuntime BackendType = "onnxruntime"
	BackendPyTorch     BackendType = "pytorch"
	BackendFIL         BackendType = "fil"
	BackendPython      BackendType = "python"
	BackendDALI        BackendType = "dali"
	BackendIdentity    BackendType = "identity"
	BackendRepeat      BackendType = "repeat"
	BackendSquare      BackendType = "square"
)

var ModelTypeToBackend = map[ModelType]BackendType{
	ModelTypeTensorRT:   BackendTensorRT,
	ModelTypeTensorFlow: BackendTensorFlow,
	ModelTypeONNX:       BackendONNXRuntime,
	ModelTypePyTorch:    BackendPyTorch,
	ModelTypeFIL:        BackendFIL,
	ModelTypePython:     BackendPython,
}

var ModelTypeToPlatform = map[ModelType]string{
	ModelTypePyTorch:    string(ModelTypePyTorch),
	ModelTypeTensorRT:   string(ModelTypeTensorRT),
	ModelTypeTensorFlow: string(ModelTypeTensorFlow),
	ModelTypeONNX:       string(ModelTypeONNX),
}

// ConfigGenerationOptions represents options for generating a model configuration
// ModelName is required, other fields are optional and will use defaults if not provided
// EnableDecoupledMode Indicates whether responses generated by the model are decoupled with
// the requests issued to it, which means the number of responses
// generated by model may differ from number of requests issued, and
// that the responses may be out of order relative to the order of
// requests. The default is false, which means the model will generate
// exactly one response for each request.
type ConfigGenerationOptions struct {
	ModelName           string
	ModelFileName       string
	MaxBatchSize        int32
	VersionPolicy       *ModelVersionPolicy
	InstanceGroups      []*ModelInstanceGroup
	Backend             string
	EnableResponseCache bool
	EnableDecoupledMode bool
	KnownModelType      ModelType
	Options             ModelTypeSpecificOptions
	Inputs              []*ModelInput
	Outputs             []*ModelOutput
}

type ModelTypeSpecificOptions interface {
	Apply(config *ModelConfig) error
}
