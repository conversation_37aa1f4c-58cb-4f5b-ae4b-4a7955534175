package types

import (
	"encoding/json"
	"fmt"
)

// TensorRTOptions represents TensorRT-specific configuration options
type TensorRTOptions struct {
	UseNonLinearIO   bool
	CCModelFilenames map[string]string
}

// Apply applies TensorRT-specific options to a model configuration
func (o *TensorRTOptions) Apply(config *ModelConfig) error {
	if o.UseNonLinearIO {
		for i := range config.Input {
			config.Input[i].IsNonLinearFormatIo = true
		}
	}

	if len(o.CCModelFilenames) > 0 {
		config.CcModelFilenames = o.CCModelFilenames
	}

	return nil
}

// TensorFlowOptions represents TensorFlow-specific configuration options
type TensorFlowOptions struct {
}

// Apply applies TensorFlow-specific options to a model configuration
func (o *TensorFlowOptions) Apply(_ *ModelConfig) error {
	return nil
}

// ONNXOptions represents ONNX-specific configuration options
type ONNXOptions struct {
}

// Apply applies ONNX-specific options to a model configuration
func (o *ONNXOptions) Apply(_ *ModelConfig) error {
	return nil
}

// TorchCompileParams represents the parameters for torch.compile()
// These parameters are used with PyTorch 2.0's torch.compile functionality
// and are passed as a JSON object in the TORCH_COMPILE_OPTIONAL_PARAMETERS config.
//
// Example usage:
//
//	options := &PyTorchOptions{
//		TorchCompile: &TorchCompileParams{
//			Disable: true,
//			Mode:    "reduce-overhead",
//		},
//	}
type TorchCompileParams struct {
	Fullgraph bool                   `json:"fullgraph,omitempty"` // Whether it is ok to break model into several subgraphs
	Dynamic   bool                   `json:"dynamic,omitempty"`   // Use dynamic shape tracing
	Backend   string                 `json:"backend,omitempty"`   // The backend to be used
	Mode      string                 `json:"mode,omitempty"`      // Can be either "default", "reduce-overhead" or "max-autotune"
	Options   map[string]interface{} `json:"options,omitempty"`   // A dictionary of options to pass to the backend
	Disable   bool                   `json:"disable,omitempty"`   // Turn torch.compile() into a no-op for testing
}

// PyTorchOptions represents PyTorch-specific configuration options. Refer: https://docs.nvidia.com/deeplearning/triton-inference-server/user-guide/docs/pytorch_backend/README.html#using-the-pytorch-backend
type PyTorchOptions struct {
	DisableOptimizedExecution bool                // DISABLE_OPTIMIZED_EXECUTION: disable optimized execution of TorchScript models (default: false)
	InferenceMode             bool                // INFERENCE_MODE: enable the Inference Mode execution (default: true)
	DisableCudnn              bool                // DISABLE_CUDNN: disable the cuDNN library (default: false)
	EnableWeightSharing       bool                // ENABLE_WEIGHT_SHARING: enable model instances to share weights on the same device (default: false)
	EnableCacheCleaning       bool                // ENABLE_CACHE_CLEANING: enable CUDA cache cleaning after each model execution (default: false)
	InterOpThreadCount        int                 // INTER_OP_THREAD_COUNT: sets the size of the inference thread pool (default: number of cpu cores)
	IntraOpThreadCount        int                 // INTRA_OP_THREAD_COUNT: sets the number of threads within ops (default: number of cpu cores)
	EnableJitExecutor         bool                // ENABLE_JIT_EXECUTOR: enable JIT executor (default: true)
	EnableJitProfiling        bool                // ENABLE_JIT_PROFILING: enable JIT profiling (default: true)
	NumThreads                int                 // (PyTorch 2.0) NUM_THREADS: number of threads used for intraop parallelism on CPU
	NumInteropThreads         int                 // (PyTorch 2.0) NUM_INTEROP_THREADS: number of threads used for interop parallelism on CPU
	TorchCompile              *TorchCompileParams // (PyTorch 2.0) TORCH_COMPILE_OPTIONAL_PARAMETERS: parameters for torch.compile()
}

// Apply applies PyTorch-specific options to a model configuration
func (o *PyTorchOptions) Apply(config *ModelConfig) error {
	config.Backend = string(BackendPyTorch)
	config.Platform = string(ModelTypePyTorch)

	params := make(map[string]*ModelParameter)

	// Set DISABLE_OPTIMIZED_EXECUTION if specified
	if o.DisableOptimizedExecution {
		params["DISABLE_OPTIMIZED_EXECUTION"] = &ModelParameter{
			StringValue: "true",
		}
	}

	// Set INFERENCE_MODE (default is true, so we only need to set it if explicitly disabled)
	if !o.InferenceMode {
		params["INFERENCE_MODE"] = &ModelParameter{
			StringValue: "false",
		}
	}

	// Set DISABLE_CUDNN if specified
	if o.DisableCudnn {
		params["DISABLE_CUDNN"] = &ModelParameter{
			StringValue: "true",
		}
	}

	// Set ENABLE_WEIGHT_SHARING if specified
	if o.EnableWeightSharing {
		params["ENABLE_WEIGHT_SHARING"] = &ModelParameter{
			StringValue: "true",
		}
	}

	// Set ENABLE_CACHE_CLEANING if specified
	if o.EnableCacheCleaning {
		params["ENABLE_CACHE_CLEANING"] = &ModelParameter{
			StringValue: "true",
		}
	}

	// Set INTER_OP_THREAD_COUNT if specified
	if o.InterOpThreadCount > 0 {
		params["INTER_OP_THREAD_COUNT"] = &ModelParameter{
			StringValue: fmt.Sprintf("%d", o.InterOpThreadCount),
		}
	}

	// Set INTRA_OP_THREAD_COUNT if specified
	if o.IntraOpThreadCount > 0 {
		params["INTRA_OP_THREAD_COUNT"] = &ModelParameter{
			StringValue: fmt.Sprintf("%d", o.IntraOpThreadCount),
		}
	}

	// Set ENABLE_JIT_EXECUTOR (default is true, so we only need to set it if explicitly disabled)
	if !o.EnableJitExecutor {
		params["ENABLE_JIT_EXECUTOR"] = &ModelParameter{
			StringValue: "false",
		}
	}

	// Set ENABLE_JIT_PROFILING (default is true, so we only need to set it if explicitly disabled)
	if !o.EnableJitProfiling {
		params["ENABLE_JIT_PROFILING"] = &ModelParameter{
			StringValue: "false",
		}
	}

	// Set NUM_THREADS if specified (PyTorch 2.0 parameter)
	if o.NumThreads > 0 {
		params["NUM_THREADS"] = &ModelParameter{
			StringValue: fmt.Sprintf("%d", o.NumThreads),
		}
	}

	// Set NUM_INTEROP_THREADS if specified (PyTorch 2.0 parameter)
	if o.NumInteropThreads > 0 {
		params["NUM_INTEROP_THREADS"] = &ModelParameter{
			StringValue: fmt.Sprintf("%d", o.NumInteropThreads),
		}
	}

	// Set TORCH_COMPILE_OPTIONAL_PARAMETERS if specified (PyTorch 2.0 parameter)
	if o.TorchCompile != nil {
		jsonData, err := json.Marshal(o.TorchCompile)
		if err != nil {
			return fmt.Errorf("failed to marshal torch compile parameters: %v", err)
		}
		params["TORCH_COMPILE_OPTIONAL_PARAMETERS"] = &ModelParameter{
			StringValue: string(jsonData),
		}
	}

	// Set the parameters in the model configuration if any were specified
	if len(params) > 0 {
		config.Parameters = params
	}

	return nil
}

// FILOptions represents Forest Inference Library-specific configuration options
type FILOptions struct {
	ModelType                    string  // model_type: xgboost_ubj, xgboost_json, xgboost, lightgbm, treelite_checkpoint
	StorageType                  string  // storage_type: DENSE, SPARSE, SPARSE8, AUTO
	Threshold                    float64 // threshold for binary classifiers (default 0.5)
	PredictProba                 bool    // predict_proba: return confidence scores for each class
	OutputClass                  bool    // output_class: true for classification, false for regression
	XGBoostAllowUnknownField     bool    // xgboost_allow_unknown_field: ignore unknown fields in XGBoost JSON
	UseExperimentalOptimizations bool    // use_experimental_optimizations: enable experimental optimizations
	ThreadsPerTree               int     // threads_per_tree: number of CUDA threads per tree (GPU only)
	Algo                         string  // algo: NAIVE, TREE_REORG, BATCH_TREE_REORG, ALGO_AUTO (GPU only)
	BlocksPerSM                  int     // blocks_per_sm: number of blocks per streaming multiprocessor (GPU only)
	TransferThreshold            int     // transfer_threshold: batch size threshold for GPU transfer (GPU only)
}

// Apply applies FIL-specific options to a model configuration
func (o *FILOptions) Apply(config *ModelConfig) error {
	// Make sure we're using the FIL backend
	config.Backend = string(BackendFIL)
	// FIL doesn't use the platform field, so clear it
	config.Platform = ""

	// Create a slice of Parameters
	params := make(map[string]*ModelParameter)

	// Add the FIL-specific parameters according to the FIL backend documentation
	if o.ModelType != "" {
		params["model_type"] = &ModelParameter{
			StringValue: o.ModelType,
		}
	}

	if o.StorageType != "" {
		params["storage_type"] = &ModelParameter{
			StringValue: o.StorageType,
		}
	}

	if o.Threshold != 0 {
		params["threshold"] = &ModelParameter{
			StringValue: fmt.Sprintf("%f", o.Threshold),
		}
	}

	if o.PredictProba {
		params["predict_proba"] = &ModelParameter{
			StringValue: "true",
		}
	}

	if o.OutputClass {
		params["output_class"] = &ModelParameter{
			StringValue: "true",
		}
	}

	if o.XGBoostAllowUnknownField {
		params["xgboost_allow_unknown_field"] = &ModelParameter{
			StringValue: "true",
		}
	}

	if o.UseExperimentalOptimizations {
		params["use_experimental_optimizations"] = &ModelParameter{
			StringValue: "true",
		}
	}

	if o.ThreadsPerTree != 0 {
		params["threads_per_tree"] = &ModelParameter{
			StringValue: fmt.Sprintf("%d", o.ThreadsPerTree),
		}
	}

	if o.Algo != "" {
		params["algo"] = &ModelParameter{
			StringValue: o.Algo,
		}
	}

	if o.BlocksPerSM != 0 {
		params["blocks_per_sm"] = &ModelParameter{
			StringValue: fmt.Sprintf("%d", o.BlocksPerSM),
		}
	}

	if o.TransferThreshold != 0 {
		params["transfer_threshold"] = &ModelParameter{
			StringValue: fmt.Sprintf("%d", o.TransferThreshold),
		}
	}

	// Set the parameters in the model configuration
	config.Parameters = params

	return nil
}
