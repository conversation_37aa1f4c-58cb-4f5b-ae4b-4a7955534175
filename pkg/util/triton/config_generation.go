package triton

import (
	"fmt"
	"io"
	"os"
	"path/filepath"

	"google.golang.org/protobuf/encoding/prototext"

	"git.garena.com/shopee/anti-fraud/ums-admin/pkg/util/triton/types"
)

// DefaultConfigGenerationOptions provides default options for model configuration generation
var DefaultConfigGenerationOptions = types.ConfigGenerationOptions{
	MaxBatchSize: 1,
	VersionPolicy: &types.ModelVersionPolicy{
		PolicyChoice: &types.ModelVersionPolicy_All_{
			All: &types.ModelVersionPolicy_All{},
		},
	},
	InstanceGroups: []*types.ModelInstanceGroup{
		{
			Kind: types.ModelInstanceGroup_KIND_CPU,
		},
	},
	EnableResponseCache: true,
	EnableDecoupledMode: false,
}

// GenerateConfigFile generates a Triton Inference Server configuration file.
func GenerateConfigFile(modelFile io.Reader, configTargetPath string, options *types.ConfigGenerationOptions) error {
	if options.ModelName == "" {
		return fmt.Errorf("ModelName is required in ConfigGenerationOptions")
	}

	if options.VersionPolicy == nil {
		options.VersionPolicy = DefaultConfigGenerationOptions.VersionPolicy
	}

	if len(options.InstanceGroups) == 0 {
		options.InstanceGroups = DefaultConfigGenerationOptions.InstanceGroups
	}

	tempFile, err := os.CreateTemp("", "model-*")
	if err != nil {
		return fmt.Errorf("failed to create temp file: %v", err)
	}

	defer os.Remove(tempFile.Name())
	defer tempFile.Close()

	if modelFile != nil {
		_, err = io.Copy(tempFile, modelFile)
		if err != nil {
			return fmt.Errorf("failed to write model to temp file: %v", err)
		}
	}

	modelType, err := detectModelType(tempFile.Name(), options.KnownModelType)
	if err != nil {
		return fmt.Errorf("failed to detect model type: %v", err)
	}

	inputs := options.Inputs
	outputs := options.Outputs

	if options.Inputs == nil || options.Outputs == nil {
		inputs, outputs, err = extractModelMetadata(tempFile.Name(), modelType)
		if err != nil {
			return fmt.Errorf("failed to extract model metadata: %v", err)
		}
	}

	config := &types.ModelConfig{
		Name:          options.ModelName,
		MaxBatchSize:  options.MaxBatchSize,
		Input:         inputs,
		Output:        outputs,
		VersionPolicy: options.VersionPolicy,
		InstanceGroup: options.InstanceGroups,
	}

	if options.ModelFileName != "" {
		config.DefaultModelFilename = options.ModelFileName
	}

	if _, ok := types.ModelTypeToBackend[modelType]; ok {
		config.Backend = string(types.ModelTypeToBackend[modelType])
	} else {
		config.Backend = string(types.BackendPython)
	}

	if _, ok := types.ModelTypeToPlatform[modelType]; ok {
		config.Platform = types.ModelTypeToPlatform[modelType]
	}

	if options.EnableResponseCache {
		config.ResponseCache = &types.ModelResponseCache{
			Enable: true,
		}
	}

	if options.EnableDecoupledMode {
		config.ModelTransactionPolicy = &types.ModelTransactionPolicy{
			Decoupled: true,
		}
	}

	if err := options.Options.Apply(config); err != nil {
		return fmt.Errorf("failed to apply model type specific options: %v", err)
	}

	return generateConfigFile(config, configTargetPath)
}

// generateConfigFile generates a Triton Inference Server configuration file (.pbtxt) from a ModelConfig.
func generateConfigFile(config *types.ModelConfig, configTargetPath string) error {
	options := prototext.MarshalOptions{
		Multiline: true,
		Indent:    "  ",
	}

	configText, err := options.Marshal(config)
	if err != nil {
		return fmt.Errorf("failed to marshal to pbtxt: %v", err)
	}

	configDir := filepath.Dir(configTargetPath)
	if err := os.MkdirAll(configDir, 0755); err != nil {
		return fmt.Errorf("failed to create config directory: %v", err)
	}

	if err := os.WriteFile(configTargetPath, configText, 0644); err != nil {
		return fmt.Errorf("failed to write config file: %v", err)
	}

	return nil
}
