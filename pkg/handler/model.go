package handler

import (
	"encoding/json"
	"errors"
	"net/http"
	"path/filepath"

	"github.com/gin-gonic/gin"
	"github.com/jinzhu/copier"
	"github.com/spf13/cast"

	"git.garena.com/shopee/anti-fraud/ums-admin/pkg/common"
	"git.garena.com/shopee/anti-fraud/ums-admin/pkg/domain"
	"git.garena.com/shopee/anti-fraud/ums-admin/pkg/entity"
	"git.garena.com/shopee/anti-fraud/ums-admin/pkg/service"
	"git.garena.com/shopee/anti-fraud/ums-admin/pkg/util"
)

type ModelHandler struct{}

func (s ModelHandler) ListModel(c *gin.Context) {
	req := &entity.ListModelRequest{}
	if err := c.ShouldBindQuery(req); err != nil {
		Base.ResponseError(c, err)
		return
	}

	sortParam := c.Query("sort")
	if sortParam != "" {
		var sort []entity.SortingParam
		if err := json.Unmarshal([]byte(sortParam), &sort); err != nil {
			c.JSON(400, gin.H{"error": "Invalid sort parameter: " + err.Error()})
			return
		}
		req.SortParams = sort
	}

	resp, err := service.ListModel(req)
	if err != nil {
		Base.ResponseError(c, err)
		return
	}
	Base.ResponseOk(c, resp)
}

func (s ModelHandler) CreateModel(c *gin.Context) {
	req := &entity.CreateModelRequest{}
	if err := c.ShouldBind(&req); err != nil {
		Base.ResponseError(c, err)
		return
	}
	userEmail := c.GetString(common.AuthUserEmail)
	if userEmail == "" {
		Base.ResponseError(c, errors.New("miss operator"))
		return
	}
	req.Model.Creator = userEmail
	req.Model.Updater = userEmail
	resp, err := service.CreateModel(req)
	if err != nil {
		Base.ResponseError(c, err)
		return
	}
	Base.ResponseOk(c, resp)
}

func (s ModelHandler) UpdateModel(c *gin.Context) {
	req := &entity.UpdateModelRequest{}
	if err := c.ShouldBind(req); err != nil {
		Base.ResponseError(c, err)
		return
	}
	userEmail := c.GetString(common.AuthUserEmail)
	if userEmail == "" {
		Base.ResponseError(c, errors.New("miss operator"))
		return
	}

	// no need review for update path even though the model is active
	if !req.IsUpdatePath && (req.NeedReview || service.NeedReview(req.Model.ID, common.ModelEntity)) {
		reviewRequest := domain.ModelReviewEntity{}
		model := req.Model
		err := copier.CopyWithOption(&reviewRequest, &model, copier.Option{
			DeepCopy: true,
		})
		if err != nil {
			Base.ResponseError(c, err)
			return
		}
		reviewRequest.ID = 0
		reviewRequest.ModelID = req.Model.ID
		reviewRequest.Action = common.ActionEdit
		reviewRequest.Submitter = userEmail
		err = service.CreateReview(reviewRequest, common.ModelEntity)
		if err != nil {
			Base.ResponseError(c, err)
			return
		}
		Base.ResponseOk(c, "")
		return
	}
	req.Model.Updater = userEmail
	resp, err := service.UpdateModel(req.Model)
	if err != nil {
		Base.ResponseError(c, err)
		return
	}
	Base.ResponseOk(c, resp)
}

func (s ModelHandler) UploadModel(c *gin.Context) {
	req := &entity.UploadModelRequest{}
	if err := c.ShouldBind(req); err != nil {
		Base.ResponseError(c, err)
		return
	}

	resp, err := service.UploadModel(req)
	if err != nil {
		Base.ResponseError(c, err)
		return
	}

	Base.ResponseOk(c, resp)
}

func (s ModelHandler) GetModelInfo(c *gin.Context) {
	id, err := cast.ToInt32E(c.Param(common.ID))
	if err != nil {
		Base.ResponseError(c, err)
		return
	}

	modelInfo, err := service.GetModelInfoByID(id)
	if err != nil {
		Base.ResponseError(c, err)
		return
	}

	Base.ResponseOk(c, modelInfo)
}

func (s ModelHandler) GetModelsStatistics(c *gin.Context) {
	monthsAgo := util.FirstDayOfMonthsAgo(11).Format("2006-01-02")
	fromDate, err := cast.StringToDate(c.DefaultQuery(common.QueryKeyFrom, monthsAgo))
	if err != nil {
		Base.ResponseError(c, err)
		return
	}

	groupBy, err := cast.ToStringE(c.DefaultQuery(common.QueryKeyGroupBy, "month"))
	if err != nil || !common.FeaturesStatisticsAllowedGroupBy[groupBy] {
		Base.ResponseError(c, errors.New("invalid group_by"))
		return
	}

	resp, err := service.GetModelsStatistics(fromDate.Unix(), groupBy)
	if err != nil {
		Base.ResponseError(c, err)
		return
	}

	Base.ResponseOk(c, resp)
}

func (s ModelHandler) GetModelStatistics(c *gin.Context) {
	id, err := cast.ToInt32E(c.Param(common.ID))
	if err != nil {
		Base.ResponseError(c, err)
		return
	}

	modelInfo, err := service.GetModelStatisticsByID(id)
	if err != nil {
		Base.ResponseError(c, err)
		return
	}

	Base.ResponseOk(c, modelInfo)
}

func (s ModelHandler) GetModelFinalFeatures(c *gin.Context) {
	req := &entity.GetModelFinalFeaturesRequest{}
	if err := c.ShouldBind(req); err != nil {
		Base.ResponseError(c, err)
		return
	}

	resp, err := service.GetModelFinalFeatures(req.ServiceName, req.ModelPath, req.ModelLoaderType)
	if err != nil {
		Base.ResponseError(c, err)
		return
	}

	Base.ResponseOk(c, resp)
}

func (s ModelHandler) GetModelFiles(c *gin.Context) {
	modelFiles, err := service.GetModelFiles()
	if err != nil {
		Base.ResponseError(c, err)
		return
	}
	Base.ResponseOk(c, modelFiles)
}

func (s ModelHandler) UploadS3Model(c *gin.Context) {
	file, err := c.FormFile("file")
	if err != nil {
		Base.ResponseError(c, err)
	}

	openFile, err := file.Open()
	if err != nil {
		Base.ResponseError(c, err)
		return
	}
	defer openFile.Close()

	modelID, err := cast.ToInt32E(c.Param(common.ModelID))
	if err != nil {
		Base.ResponseError(c, err)
		return
	}
	serviceName := c.PostForm("service_name")
	fileType := c.PostForm("file_type")
	forceUpload := cast.ToBool(c.PostForm("force_upload"))
	fileExt := filepath.Ext(file.Filename)

	modelInfo, err := service.GetModelInfoByID(modelID)
	if err != nil {
		Base.ResponseError(c, err)
		return
	}

	filePath, err := service.UploadS3File(openFile, serviceName, modelInfo, fileExt, fileType, file.Size, forceUpload)
	if err != nil {
		Base.ResponseError(c, err)
		return
	}

	Base.ResponseOk(c, filePath)
}

func (s ModelHandler) DownloadModelFile(c *gin.Context) {
	modelID, err := cast.ToInt32E(c.Param(common.ID))
	if err != nil {
		Base.ResponseError(c, err)
		return
	}

	filePath, err := service.GetModelFileURL(modelID)
	if err != nil {
		Base.ResponseError(c, err)
		return
	}

	c.Redirect(http.StatusFound, filePath)
}
