package common

import (
	"git.garena.com/shopee/anti-fraud/ums-admin/pkg/util/triton/types"
	umsConfig "git.garena.com/shopee/anti-fraud/unified-model-service/v2/pkg/config"
)

const (
	ErrConfigKey = "missing_config_key"

	IsValid                = 1
	InvalidModelID         = "invalid model id"
	InvalidServiceID       = "invalid service id"
	InvalidFeatureID       = "invalid feature id"
	NoModelFound           = "no model found"
	NoFeatureFound         = "no feature found"
	NoServiceFound         = "no service found"
	ServiceExist           = "service is existing"
	ModelNotExist          = "model is not existing"
	FeatureNotExist        = "feature is not existing"
	ModelFeatureNotExist   = "model feature pair is not existing"
	RequestNotExist        = "request is not existing"
	UserPermissionNotExist = "user permission is not existing"
	TrialNotExist          = "trial is not existing"
	InvalidModel           = "invalid model"
	Dsn                    = "%s:%s@tcp(%s)/%s?charset=utf8mb4&parseTime=True&loc=Local"
	ID                     = "id"
	ServiceID              = "service_id"
	ModelID                = "model_id"
	FeatureID              = "feature_id"
	IsRequired             = "is_required"
	MetricCenter           = "metrics_center"
	KeyTypeParam           = "param"
	KeyTypeDateTime        = "datetime"
	KeyTypeLocalTime       = "localtime"
	KeyTypeLocalTime4Am    = "localtime4am"
	MissParam              = "miss param"
	DataTime               = "date"
	ShortForm              = "short_form"
	QueryKeyFrom           = "from"
	QueryKeyGroupBy        = "group_by"

	ConfKeySpex    = "spex_cfg"
	ConfKeyKms     = "kms"
	ConfKeyAuthURI = "auth_urls"

	CmdGetMultiMetrics              = "anti_fraud.metrics.reader.get_multi_metrics"
	CmdGetMetricsList               = "anti_fraud.metrics.manage.get_all_metrics_info"
	CmdGetMetricsListFeatureStore   = "anti_fraud.feature_store.admin_portal.get_feature_list"
	CmdGetMetricsDetailFeatureStore = "anti_fraud.feature_store.admin_portal.get_feature_detail_by_id"

	CallMcError = "call metric_center error"
	CallFSError = "call feature store error"

	AuthUserID          = "user_id"
	AuthUserEmail       = "user_email"
	AuthUserRole        = "user_role"
	AuthCookie          = "tk"
	RequestID           = "request_id"
	AuthClientIDHeader  = "X-Client-ID"
	AuthSecretKeyHeader = "X-Secret-Key"

	ProjectName      = "ums"
	ProjectID        = 1001
	ProjectRoleAdmin = "admin"

	ReqKeyServiceName = "service_name"

	DefaultLimit = 20

	KmsKeyDBPwd         = "48072:db_pwd"         // nolint: gosec
	KmsKeyS3Secret      = "48072:s3_secret"      // nolint: gosec
	KmsKeyClientSecrets = "48072:client_secrets" // nolint: gosec

	HeaderContentType = "Content-Type"
	ContentTypeJSON   = "application/json"
	ReadTimeout       = 30

	APIV1Path               = "/api/ums"
	PathStatistics          = "/statistics"
	PathServices            = "/services"
	PathModels              = "/models"
	PathFeatures            = "/features"
	PathModelFeatures       = "/model_features"
	PathRequests            = "/requests"
	PathTrials              = "/trials"
	PathAuditLogs           = "/audit_logs"
	PathExternalMc          = "/metrics"
	PathExpression          = "/expression"
	PathPermissions         = "/permissions"
	PathFeatureHistory      = "/feature_history"
	PathModelFeatureHistory = "/model_feature_history"
	PathReview              = "/review"

	ExistTicket       = "exist a ticket need process"
	UnavailableTicket = "ticket status is unavailable"
	UnavailableEntity = "unavailable entity"

	ServiceEntity = "service"
	ModelEntity   = "model"
	FeatureEntity = "feature"

	StatusRequested = "REQUESTED"
	StatusCanceled  = "CANCELED"
	StatusApproved  = "APPROVED"
	StatusRejected  = "REJECTED"
	StatusCompleted = "COMPLETED"

	ActionEdit        = "EDIT"
	ActionActive      = "ACTIVE"
	ActionBindFeature = "BIND FEATURE"
	ActionRollback    = "ROLLBACK"
	StatusActive      = 1
)

var (
	FeaturesStatisticsAllowedGroupBy = map[string]bool{
		"month": true,
		"day":   true,
		"hour":  true,
		"week":  true,
		"year":  true,
	}

	QueryOrderMap = map[int8]string{
		1:  "asc",
		-1: "desc",
	}
)

const (
	LoaderTypePyTorch = "pytorch"
)

var LoaderTypeToModelType = map[string]types.ModelType{
	umsConfig.LoaderXGBoost:    types.ModelTypeFIL,
	umsConfig.LoaderXGBoostV17: types.ModelTypeFIL,
	umsConfig.LoaderLightGBM:   types.ModelTypeFIL,
	umsConfig.LoaderXGBoostV08: types.ModelTypeFIL,
	LoaderTypePyTorch:          types.ModelTypePyTorch,
}
