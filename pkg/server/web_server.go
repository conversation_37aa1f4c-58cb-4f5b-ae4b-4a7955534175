package server

import (
	"net/http"
	"os"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"

	"git.garena.com/shopee/anti-fraud/ums-admin/pkg/common"
	"git.garena.com/shopee/anti-fraud/ums-admin/pkg/server/middleware"

	"git.garena.com/shopee/anti-fraud/ums-admin/pkg/handler"
)

type Option struct {
	Port    string `yaml:"port"`
	APIHost string `yaml:"apiHost"`
}

func LoadHTTPAddress() string {
	_port, present := os.LookupEnv("PORT")
	if !present {
		return "127.0.0.1:8083"
	}
	return "0.0.0.0" + ":" + _port
}

// StartServer http server
func StartServer() {
	r := gin.Default()
	cors.Default()
	r.Use(CORSMiddleware())

	r.Use(middleware.IdentifyUser())
	r.Use(middleware.AuditLogger())
	Register(r)
	address := LoadHTTPAddress()
	if err := r.Run(address); err != nil {
		panic(err)
	}
}

func CORSMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Writer.Header().Set("Access-Control-Allow-Origin", c.Request.Header.Get("Origin"))
		c.Writer.Header().Set("Access-Control-Allow-Credentials", "true")
		c.Writer.Header().Set("Access-Control-Allow-Headers", "Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, accept, origin, Cache-Control, X-Requested-With")
		c.Writer.Header().Set("Access-Control-Allow-Methods", "POST, HEAD, PATCH, GET, PUT, DELETE, OPTIONS")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	}
}

var (
	statisticsHandler          = &handler.StatisticsHandler{}
	serviceHandler             = &handler.ServiceHandler{}
	modelHandler               = &handler.ModelHandler{}
	modelFeatureHandler        = &handler.ModelFeatureHandler{}
	featureHandler             = &handler.FeatureHandler{}
	requestHandler             = &handler.RequestHandler{}
	mcHandler                  = &handler.MetricCenterHandler{}
	auditLogHandler            = &handler.AuditLogHandler{}
	trialHandler               = &handler.TrialHandler{}
	externalHandler            = &handler.ExternalHandler{}
	expressionHandler          = &handler.ExpressionHandler{}
	featureHistoryHandler      = &handler.FeatureHistoryHandler{}
	modelFeatureHistoryHandler = &handler.ModelFeatureHistoryHandler{}
	userPermissionHandler      = &handler.UserPermissionHandler{}
	reviewHandler              = &handler.ReviewHandler{}
)

func Register(e *gin.Engine) {
	apiNoAuth := e.Group(common.APIV1Path)

	externalAPI := e.Group(common.APIV1Path + "/external")
	externalAPI.Use(middleware.AuthSecretKey())
	e.GET("/api/health_check/", func(c *gin.Context) {
		c.SetCookie("tk", "0887629fbe31c62060e056a5759642851dca2d0896723f7726531b646930f328", 31536000, "/", "localhost", false, true)
		c.Status(http.StatusOK)
	})

	apiWithProjectMemberAuthorization := e.Group(common.APIV1Path)
	apiWithProjectMemberAuthorization.Use(middleware.AuthUserID())

	apiWithPermissionAuthorization := e.Group(common.APIV1Path)
	apiWithPermissionAuthorization.Use(middleware.AuthUserID())
	apiWithPermissionAuthorization.Use(middleware.AuthorizeUser())

	apiWithProjectAdminAuthorization := e.Group(common.APIV1Path)
	apiWithProjectAdminAuthorization.Use(middleware.AuthUserID())
	apiWithProjectAdminAuthorization.Use(middleware.AuthProjectAdmin())

	apiNoAuth.GET(common.PathServices, serviceHandler.ListService)
	apiNoAuth.GET(common.PathStatistics, statisticsHandler.ListStatistics)
	apiNoAuth.GET(common.PathServices+"/:id", serviceHandler.GetService)
	apiWithProjectAdminAuthorization.PUT(common.PathServices, serviceHandler.CreateService)
	apiWithPermissionAuthorization.PATCH(common.PathServices, serviceHandler.UpdateService)

	apiNoAuth.GET(common.PathModels, modelHandler.ListModel)
	apiNoAuth.GET(common.PathModels+"/statistics", modelHandler.GetModelsStatistics)
	apiNoAuth.GET(common.PathModels+"/:id", modelHandler.GetModelInfo)
	apiNoAuth.GET(common.PathModels+"/:id/statistics", modelHandler.GetModelStatistics)
	apiNoAuth.GET(common.PathModels+"/:id/final_features", modelHandler.GetModelFinalFeatures)
	apiNoAuth.GET(common.PathModels+"/model_files", modelHandler.GetModelFiles)
	apiWithPermissionAuthorization.PUT(common.PathModels+"/:model_id/features/:feature_id", modelFeatureHandler.BindFeatureToModel)
	apiWithPermissionAuthorization.POST(common.PathModels+"/:model_id/features/verify", modelFeatureHandler.VerifyModelFeatures)
	apiWithPermissionAuthorization.PUT(common.PathModels, modelHandler.CreateModel)
	apiWithPermissionAuthorization.PATCH(common.PathModels+"/:id", modelHandler.UpdateModel)
	apiWithPermissionAuthorization.GET(common.PathModels+"/:id/model_file", modelHandler.DownloadModelFile)
	apiWithPermissionAuthorization.POST(common.PathModels+"/upload", modelHandler.UploadModel)
	apiWithPermissionAuthorization.POST(common.PathModels+"/:model_id/model_files", modelHandler.UploadS3Model)

	apiNoAuth.PUT(common.PathFeatures, featureHandler.CreateFeature)
	apiNoAuth.POST(common.PathFeatures+"/duplications", featureHandler.FindDuplicatedFeatures)
	apiWithPermissionAuthorization.PATCH(common.PathFeatures, featureHandler.UpdateFeature)
	apiNoAuth.GET(common.PathFeatures, featureHandler.ListFeature)
	apiNoAuth.GET(common.PathFeatures+"/functions", featureHandler.ListFunctions)
	apiNoAuth.GET(common.PathFeatures+"/statistics", featureHandler.ListFeaturesStatistics)
	apiNoAuth.POST(common.PathFeatures+"/query", featureHandler.QueryFeature)
	apiNoAuth.GET(common.PathFeatures+"/:id", featureHandler.GetFeature)
	apiNoAuth.GET(common.PathFeatures+"/:id/models", featureHandler.GetFeatureModels)
	apiNoAuth.GET(common.PathFeatures+"/:id/services", featureHandler.GetFeatureServices)
	apiWithPermissionAuthorization.DELETE(common.PathFeatures+"/:id", featureHandler.DeleteFeature)

	apiNoAuth.GET(common.PathModelFeatures, modelFeatureHandler.ListModelFeature)
	apiNoAuth.POST(common.PathModelFeatures+"/visual", modelFeatureHandler.VisualizeModelFeatures)
	apiWithPermissionAuthorization.PUT(common.PathModelFeatures, modelFeatureHandler.CreateModelFeature)
	apiWithPermissionAuthorization.PATCH(common.PathModelFeatures, modelFeatureHandler.UpdateModelFeature)
	apiWithPermissionAuthorization.DELETE(common.PathModelFeatures, modelFeatureHandler.DeleteModelFeature)

	apiNoAuth.GET(common.PathRequests, requestHandler.ListRequest)
	apiWithPermissionAuthorization.PUT(common.PathRequests, requestHandler.CreateRequest)
	apiWithPermissionAuthorization.PATCH(common.PathRequests, requestHandler.UpsertRequest)
	apiWithPermissionAuthorization.DELETE(common.PathRequests+"/:id", requestHandler.DeleteRequest)

	apiNoAuth.GET(common.PathTrials, trialHandler.ListTrials)
	apiNoAuth.GET(common.PathTrials+"/:id", trialHandler.GetTrialDetail)
	apiNoAuth.POST(common.PathTrials+"/:id", trialHandler.InquiryTrial)
	apiNoAuth.PUT(common.PathTrials, trialHandler.CreateTrial)

	apiNoAuth.GET(common.PathExternalMc, mcHandler.GetMetricsList)
	apiNoAuth.GET(common.PathExternalMc+"/:id", mcHandler.GetMetricsDetail)

	apiWithProjectAdminAuthorization.GET(common.PathAuditLogs, auditLogHandler.GetAuditLogs)

	externalAPI.GET("/services", externalHandler.ListServices)
	externalAPI.GET("/features", externalHandler.ListFeatures)
	externalAPI.POST("/models/feature-store", externalHandler.ListModelByMetrics)

	externalAPI.POST("/graph/query", externalHandler.GraphQL)

	apiNoAuth.POST(common.PathExpression+"/valid", expressionHandler.ValidateExpression)
	apiNoAuth.GET(common.PathExpression+"/functions", expressionHandler.FunctionList)

	apiNoAuth.GET(common.PathFeatureHistory, featureHistoryHandler.ListFeatureHistory)
	apiWithProjectAdminAuthorization.POST(common.PathFeatureHistory+"/rollback", featureHistoryHandler.RollbackFeatureConfig)

	apiNoAuth.GET(common.PathModelFeatureHistory, modelFeatureHistoryHandler.ListModelFeatureHistory)
	apiWithProjectAdminAuthorization.POST(common.PathModelFeatureHistory+"/rollback", modelFeatureHistoryHandler.RollbackModelFeatureBinds)

	apiNoAuth.GET(common.PathPermissions, userPermissionHandler.ListUserPermission)
	apiWithProjectAdminAuthorization.PUT(common.PathPermissions, userPermissionHandler.CreateUserPermission)
	apiWithProjectAdminAuthorization.DELETE(common.PathPermissions+"/:id", userPermissionHandler.DeleteUserPermission)
	apiWithProjectAdminAuthorization.PATCH(common.PathPermissions+"/:id", userPermissionHandler.UpdateUserPermission)

	apiNoAuth.POST(common.PathReview, reviewHandler.Publish)
	apiNoAuth.POST(common.PathReview+"/list", reviewHandler.List)
	apiNoAuth.GET(common.PathReview+"/:entity/:id", reviewHandler.Get)
	apiNoAuth.PATCH(common.PathReview, reviewHandler.Update)
}
