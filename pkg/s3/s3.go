/*
Package s3 contains the s3 object storage operations

used to load model files
*/
package s3

import (
	"context"
	"errors"
	"fmt"
	"io"
	"net/url"
	"sync"
	"time"

	"git.garena.com/shopee/anti-fraud/ums-admin/pkg/common"
	"git.garena.com/shopee/anti-fraud/ums-admin/pkg/dao"
	"git.garena.com/shopee/anti-fraud/ums-admin/pkg/util/kms"
	umsUtil "git.garena.com/shopee/anti-fraud/unified-model-service/v2/pkg/util"
	"git.garena.com/shopee/common/ulog"

	"github.com/robfig/cron"

	"git.garena.com/shopee/anti-fraud/ums-admin/pkg/util"

	spcfg "git.garena.com/shopee/anti-fraud/unified-model-service/v2/pkg/config/spex"

	"git.garena.com/shopee/anti-fraud/unified-model-service/v2/pkg/base"

	"github.com/minio/minio-go/v7"
	"github.com/minio/minio-go/v7/pkg/credentials"
)

var once sync.Once

type Client struct {
	client *minio.Client
	config *spcfg.S3Config
}

var instance *Client

func GetClient() *Client {
	return instance
}

// CreateInstance ...
func CreateInstance() (setupError error) {
	once.Do(func() {
		configRaw, err := spcfg.LoadConfig(spcfg.KeyS3)
		if err != nil {
			setupError = err
			return
		}
		s3Config, ok := configRaw.(*spcfg.S3Config)
		if !ok {
			setupError = errors.New(base.ErrInvalidConfig)
			return
		}

		s3SecretKey, err := kms.Kms.GetValueFromSecurityKey(common.KmsKeyS3Secret)

		if err != nil {
			setupError = err
			return
		}

		m, err := minio.New(s3Config.Endpoint, &minio.Options{
			Creds:  credentials.NewStaticV4(s3Config.AccessKey, s3SecretKey, ""),
			Secure: true,
		})
		if err != nil {
			setupError = err
			return
		}
		s := &Client{
			client: m,
			config: s3Config,
		}
		instance = s
	})

	return setupError
}

func SyncFileInSchedule() error {
	c := cron.New()
	// Every day at 2:00am.
	cronExp := "0 2 * * *"

	loader := func() {
		defer func() {
			if r := recover(); r != nil {
				fmt.Println("SyncFileInSchedule panic:", r)
			}
		}()

		models, err := dao.ListModel(nil, nil, nil, 0, 0, nil)
		if err != nil {
			util.Logger().Error("list models from db failed", ulog.Error(err))
			return
		}
		for _, model := range models {
			err = instance.GetObject(model.ServiceName, model.Path)
			if err != nil {
				util.Logger().Error("get object from s3 failed", ulog.Error(err))
				// continue without return
			}
		}
	}

	// Run once at startup.
	go loader()
	err := c.AddFunc(cronExp, loader)
	if err != nil {
		util.Logger().Error("failed to add scheduler", ulog.Error(err))
		return err
	}

	c.Start()
	return nil
}

func (c *Client) GetObject(serviceName, modelPath string) error {
	ctx, cancel := context.WithTimeout(context.Background(), c.config.GetTimeout)
	defer cancel()

	s3Path := umsUtil.RemoteModelPath(serviceName, modelPath)
	localPath := umsUtil.LocalModelPath(serviceName, modelPath)
	err := c.client.FGetObject(ctx,
		c.config.Bucket,
		s3Path,
		localPath,
		minio.GetObjectOptions{},
	)
	if err != nil {
		util.Logger().WithField("err", err).Error("get object failed")
		return err
	}

	fp := umsUtil.FeatureMapPath(modelPath)
	fmapS3 := umsUtil.RemoteModelPath(serviceName, fp)
	if flag, err := c.IsObjectExist(fmapS3); !flag || err != nil {
		return nil
	}
	fmapLocal := umsUtil.LocalModelPath(serviceName, fp)
	err = c.client.FGetObject(ctx,
		c.config.Bucket,
		fmapS3,
		fmapLocal,
		minio.GetObjectOptions{},
	)
	if err != nil {
		util.Logger().WithField("err", err).Error("get object failed")
		return err
	}
	util.Logger().WithField("object path", s3Path).WithField("local file path", localPath).
		Info("Download file successfully")
	return nil
}

func (c *Client) IsObject(serviceName, modelPath string) (bool, error) {
	ctx, cancel := context.WithTimeout(context.Background(), c.config.GetTimeout)
	defer cancel()

	s3Path := umsUtil.RemoteModelPath(serviceName, modelPath)
	_, err := c.client.StatObject(ctx,
		c.config.Bucket,
		s3Path,
		minio.GetObjectOptions{},
	)
	if err == nil {
		return true, nil
	}

	return false, err
}

func (c *Client) UploadFile(file io.Reader, path string, size int64) (string, error) {
	ctx, cancel := context.WithTimeout(context.Background(), instance.config.GetTimeout)
	defer cancel()
	info, err := instance.client.PutObject(ctx, instance.config.Bucket, path, file, size, minio.PutObjectOptions{})
	if err != nil {
		return "", err
	}
	return info.Key, nil
}

func (c *Client) IsObjectExist(path string) (bool, error) {
	ctx, cancel := context.WithTimeout(context.Background(), instance.config.GetTimeout)
	defer cancel()
	object, _ := instance.client.StatObject(ctx, instance.config.Bucket, path, minio.StatObjectOptions{})
	return object.Key == path, nil
}

func (c *Client) RenameObject(path string) error {
	ctx, cancel := context.WithTimeout(context.Background(), instance.config.GetTimeout)
	defer cancel()

	timestamp := time.Now().Format("20060102150405")
	objectName := fmt.Sprintf("%s-%s", path, timestamp)

	object, err := instance.client.GetObject(ctx, instance.config.Bucket, path, minio.GetObjectOptions{})
	if err != nil {
		return err
	}
	stat, _ := object.Stat()
	_, err = instance.client.PutObject(ctx, instance.config.Bucket, objectName, object, stat.Size,
		minio.PutObjectOptions{})
	if err != nil {
		return err
	}

	err = instance.client.RemoveObject(ctx, instance.config.Bucket, path, minio.RemoveObjectOptions{})
	if err != nil {
		return err
	}
	return nil
}

func (c *Client) GetPreSignedGetObject(path string) (string, error) {
	ctx, cancel := context.WithTimeout(context.Background(), instance.config.GetTimeout)
	defer cancel()

	reqParams := make(url.Values)
	reqParams.Set("response-content-disposition", "attachment;")

	objectURL, err := instance.client.PresignedGetObject(ctx, instance.config.Bucket, path, 30*time.Minute, reqParams)
	if err != nil {
		return "", err
	}

	return objectURL.String(), nil
}
